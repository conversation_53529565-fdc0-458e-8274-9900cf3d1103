/* Admin Dashboard Styles - Professional Government/Police Theme */
.admin-container {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: 'Times New Roman', '<PERSON><PERSON>', <PERSON><PERSON>, serif;
  line-height: 1.5;
}

/* Modal styles for article viewing */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 8px;
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.close-button {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 1001;
}

.close-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

/* Header Styles - Government Red Theme */
.admin-header {
  background: #d90000;
  color: white;
  padding: 1.2rem 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-bottom: 2px solid #ffd700;
}

.header-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.header-logo {
  height: 45px;
  width: auto;
}

.admin-header h1 {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  letter-spacing: 0.8px;
  text-transform: uppercase;
  font-family: 'Times New Roman', serif;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}

.admin-info {
  font-weight: 600;
  color: #ffd700;
  font-size: 14px;
}

.logout-btn {
  background: #2f4f2f;
  color: white;
  border: 1px solid #2f4f2f;
  padding: 8px 16px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 13px;
}

.logout-btn:hover {
  background: #1e3e1e;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Navigation Styles - Clean Government Design */
.admin-nav {
  background: white;
  padding: 0 2rem;
  border-bottom: 1px solid #ddd;
  display: flex;
  gap: 0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.admin-nav button {
  padding: 16px 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Times New Roman', serif;
}

.admin-nav button:hover {
  background: #f8f9fa;
  color: #d90000;
}

.admin-nav button.active {
  border-bottom-color: #d90000;
  color: #d90000;
  background: #fafafa;
}

.admin-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Login Container - Professional Government Design */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Times New Roman', serif;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(217, 0, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
}

.login-background {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border: 2px solid #e0e0e0;
  border-top: 4px solid #d90000;
  overflow: hidden;
  min-width: 450px;
  max-width: 500px;
}

.government-seal {
  background: linear-gradient(135deg, #d90000 0%, #b30000 100%);
  padding: 30px 20px;
  text-align: center;
  border-bottom: 3px solid #ffd700;
  position: relative;
}

.government-seal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/images/bg-head.png');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  pointer-events: none;
}

.login-logo {
  height: 80px;
  width: auto;
  position: relative;
  z-index: 1;
  display: block;
  margin: 0 auto;
  max-width: 100%;
}

/* Login Form Styles - Government Standard */
.login-form {
  padding: 40px 35px 30px;
  background: white;
}

.login-header {
  text-align: center;
  margin-bottom: 35px;
}

.login-header h2 {
  margin: 0 0 8px 0;
  font-size: 26px;
  font-weight: 700;
  color: #d90000;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  font-family: 'Times New Roman', serif;
}

.login-header h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5px;
}

.login-header p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  font-style: italic;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 0.5px;
  font-family: 'Times New Roman', serif;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
  font-family: Arial, sans-serif;
}

.form-group input:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

.login-form button {
  width: 100%;
  padding: 14px;
  background: #d90000;
  color: white;
  border: none;
  border-radius: 2px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: 'Times New Roman', serif;
}

.login-form button:hover:not(:disabled) {
  background: #c70000;
}

.login-form button:disabled {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.login-submit-btn {
  width: 100%;
  padding: 16px;
  background: #d90000;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Times New Roman', serif;
  margin-top: 10px;
}

.login-submit-btn:hover:not(:disabled) {
  background: #c70000;
  box-shadow: 0 2px 8px rgba(217, 0, 0, 0.3);
}

.login-submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

.login-message {
  margin-top: 16px;
  padding: 12px 16px;
  border-radius: 4px;
  font-weight: 600;
  text-align: center;
  font-size: 14px;
  border-left: 4px solid;
}

.login-message.success {
  background: #f0f8f0;
  color: #2f7d32;
  border-left-color: #2f7d32;
}

.login-message.error {
  background: #fdf2f2;
  color: #d90000;
  border-left-color: #d90000;
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
  padding-top: 20px;
}

.login-footer p {
  margin: 4px 0;
  font-size: 12px;
  color: #888;
  line-height: 1.3;
}

.login-footer p:first-child {
  font-weight: 600;
  color: #666;
}

/* Feedback Management Styles */
.feedback-management {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
}

.feedback-stats {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  text-align: center;
  border-left: 4px solid #c8102e;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card h3 {
  margin: 0 0 1rem 0;
  color: #6c757d;
  font-size: 0.9rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 700;
  color: #c8102e;
  margin: 0;
}

.stat-card.pending {
  border-left-color: #ffc107;
}

.stat-card.pending .stat-number {
  color: #ffc107;
}

.stat-card.in-progress {
  border-left-color: #17a2b8;
}

.stat-card.in-progress .stat-number {
  color: #17a2b8;
}

.stat-card.resolved {
  border-left-color: #28a745;
}

.stat-card.resolved .stat-number {
  color: #28a745;
}

.stat-card.rejected {
  border-left-color: #dc3545;
}

.stat-card.rejected .stat-number {
  color: #dc3545;
}

/* Table Styles - Government Standard */
.feedbacks-table-container {
  overflow-x: auto;
  margin-bottom: 24px;
  border: 1px solid #ddd;
}

.feedbacks-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.feedbacks-table th,
.feedbacks-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  font-size: 14px;
}

.feedbacks-table th {
  background: #f8f9fa;
  font-weight: 700;
  color: #333;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #ddd;
  font-family: 'Times New Roman', serif;
}

.feedbacks-table tbody tr:hover {
  background: #f9f9f9;
}

.feedbacks-table tbody tr:nth-child(even) {
  background: #fafafa;
}

.feedbacks-table tbody tr:nth-child(even):hover {
  background: #f5f5f5;
}

.status-badge {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-select {
  padding: 0.3rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 12px 40px rgba(0,0,0,0.2);
}

.modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 2px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #c8102e;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-close {
  background: none;
  border: none;
  font-size: 2rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.modal-body {
  padding: 2rem;
}

.feedback-details h4 {
  margin: 1.5rem 0 0.8rem 0;
  color: #c8102e;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 1rem;
  letter-spacing: 0.5px;
  border-left: 4px solid #c8102e;
  padding-left: 1rem;
}

.feedback-details h4:first-child {
  margin-top: 0;
}

.content-box,
.answer-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  line-height: 1.6;
}

.answer-box {
  background: rgba(200, 16, 46, 0.02);
  border-color: rgba(200, 16, 46, 0.1);
}

.answer-textarea {
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
}

.answer-textarea:focus {
  outline: none;
  border-color: #c8102e;
  box-shadow: 0 0 0 3px rgba(200, 16, 46, 0.1);
}

.modal-footer {
  padding: 1.5rem 2rem;
  border-top: 2px solid #e9ecef;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

/* Button Styles - Government Standard */
.btn-primary,
.btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 2px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 13px;
  font-family: 'Times New Roman', serif;
}

.btn-primary {
  background: #d90000;
  color: white;
  border: 1px solid #d90000;
}

.btn-primary:hover:not(:disabled) {
  background: #c70000;
}

.btn-primary:disabled {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background: #2f4f2f;
  color: white;
  border: 1px solid #2f4f2f;
}

.btn-secondary:hover {
  background: #1e3e1e;
}

/* Action Buttons - Government Standard */
.actions-cell button {
  padding: 6px 12px;
  margin-right: 6px;
  border: 1px solid;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
  font-family: Arial, sans-serif;
}

.btn-view {
  background: #007acc;
  color: white;
  border-color: #007acc;
}

.btn-view:hover {
  background: #0066aa;
  border-color: #0066aa;
}

.btn-edit {
  background: #f6c600;
  color: #333;
  border-color: #f6c600;
}

.btn-edit:hover {
  background: #e0b000;
  border-color: #e0b000;
}

.btn-delete {
  background: #d90000;
  color: white;
  border-color: #d90000;
}

.btn-delete:hover {
  background: #c70000;
  border-color: #c70000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .header-left {
    justify-content: center;
  }
  
  .admin-nav {
    padding: 0 1rem;
    flex-wrap: wrap;
    gap: 0;
  }
  
  .admin-nav button {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }
  
  .admin-content {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .modal-overlay {
    padding: 1rem;
  }
  
  .modal-content {
    margin: 1rem 0;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .modal-footer {
    flex-direction: column;
  }
}

/* Create Article Styles - Government Professional */
.create-article {
  background: white;
  border-radius: 4px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #ddd;
}

.create-article-header {
  background: #d90000;
  color: white;
  padding: 24px;
  text-align: center;
  border-bottom: 2px solid #ffd700;
}

.create-article-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Times New Roman', serif;
}

.create-article-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.article-form-container {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.article-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  flex: 1;
}

.form-group.half-width {
  flex: 1;
}

.form-group label {
  font-weight: 700;
  color: #d90000;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Times New Roman', serif;
}

.form-input-title {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 16px;
  font-weight: 600;
  transition: border-color 0.2s ease;
  background: white;
  font-family: 'Times New Roman', serif;
}

.form-input-title:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

.form-select {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 14px;
  font-weight: 600;
  background: white;
  transition: border-color 0.2s ease;
  font-family: Arial, sans-serif;
}

.form-select:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

/* Image Upload Container Styles */
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-type-tabs {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background: #f8f9fa;
}

.tab-button {
  flex: 1;
  padding: 10px 16px;
  border: none;
  background: #f8f9fa;
  color: #666;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: Arial, sans-serif;
}

.tab-button:hover {
  background: #e9ecef;
  color: #333;
}

.tab-button.active {
  background: #d90000;
  color: white;
}

.file-upload-section,
.url-input-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
  font-family: Arial, sans-serif;
}

.form-input:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

.form-input-file {
  padding: 10px 12px;
  border: 1px dashed #ddd;
  border-radius: 2px;
  background: #fafafa;
  transition: border-color 0.2s ease;
  cursor: pointer;
  font-family: Arial, sans-serif;
}

.form-input-file:hover {
  border-color: #d90000;
  background: #f9f9f9;
}

.form-hint {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.form-textarea {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  background: white;
  transition: border-color 0.2s ease;
  font-family: 'Times New Roman', serif;
}

.form-textarea:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

.editor-container {
  border: 1px solid #ddd;
  border-radius: 2px;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.editor-container:focus-within {
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

.form-actions {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  padding-top: 24px;
  border-top: 1px solid #ddd;
}

.admin-message {
  margin-top: 20px;
  padding: 12px 16px;
  border-radius: 2px;
  font-weight: 600;
  text-align: center;
  border-left: 3px solid;
  font-size: 14px;
}

.admin-message.success {
  background: #f0f8f0;
  color: #2f7d32;
  border-left-color: #2f7d32;
}

.admin-message.error {
  background: #fdf2f2;
  color: #d90000;
  border-left-color: #d90000;
}

/* Article Management Styles - Government Standard */
.article-management .management-header h2 {
  color: #d90000;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Times New Roman', serif;
  font-size: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-text {
  font-size: 16px;
  font-weight: 600;
  color: #d90000;
}

.title-cell {
  max-width: 400px;
}

.title-text {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-family: 'Times New Roman', serif;
}

.summary-text {
  font-size: 13px;
  color: #666;
  font-style: italic;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-badge {
  background: #f0f8f0;
  color: #2f7d32;
  padding: 4px 8px;
  border-radius: 2px;
  border: 1px solid #e0e0e0;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.no-articles {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
  font-size: 1.1rem;
}

.article-preview {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  border-left: 4px solid #c8102e;
}

.warning-text {
  color: #dc3545;
  font-weight: 600;
  text-align: center;
  margin-top: 1rem;
}

/* Responsive Design for Forms */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .form-group.half-width {
    width: 100%;
  }
  
  .article-form-container {
    padding: 1.5rem;
  }
  
  .create-article-header {
    padding: 1.5rem;
  }
  
  .create-article-header h2 {
    font-size: 1.4rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
}

.loading::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #c8102e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Search and Filter Controls - Government Standard */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
}

.management-header h2 {
  margin: 0;
  color: #d90000;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.management-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input,
.category-filter,
.status-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 2px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
  font-family: Arial, sans-serif;
}

.search-input:focus,
.category-filter:focus,
.status-filter:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 2px rgba(217, 0, 0, 0.1);
}

.search-input {
  min-width: 200px;
}

/* Pagination - Government Standard */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ddd;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 2px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  font-size: 13px;
  font-family: 'Times New Roman', serif;
}

.pagination-btn:hover:not(:disabled) {
  background: #d90000;
  color: white;
  border-color: #d90000;
}

.pagination-btn:disabled {
  background: #f8f9fa;
  color: #999;
  cursor: not-allowed;
  border-color: #e0e0e0;
}

.pagination-info {
  font-weight: 600;
  color: #d90000;
  font-size: 14px;
  font-family: 'Times New Roman', serif;
}

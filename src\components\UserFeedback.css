/* User Feedback Form Styles - Government Standard */
.feedback-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 20px;
  background: #fff;
  min-height: 100vh;
  font-family: 'Times New Roman', serif;
}

.feedback-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #d90000 0%, #b30000 100%);
  color: white;
  border-radius: 8px;
  border: 3px solid #ffd700;
}

.feedback-header h1 {
  margin: 0 0 15px 0;
  font-size: 32px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.feedback-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.6;
}

.feedback-form {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 8px;
  border: 2px solid #ddd;
  margin-bottom: 40px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 700;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 15px;
  font-family: 'Times New Roman', serif;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #d90000;
  box-shadow: 0 0 0 3px rgba(217, 0, 0, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #888;
  font-style: italic;
}

.error-message {
  background: #ffe6e6;
  color: #d8000c;
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid #d8000c;
  margin-bottom: 20px;
  font-weight: 600;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.submit-btn {
  background: linear-gradient(135deg, #d90000 0%, #b30000 100%);
  color: white;
  border: 2px solid #d90000;
  padding: 15px 40px;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Times New Roman', serif;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #b30000 0%, #8b0000 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(217, 0, 0, 0.3);
}

.submit-btn:disabled {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn:active {
  transform: translateY(0);
}

/* Success State */
.feedback-success {
  text-align: center;
  padding: 60px 40px;
  background: #f0f8f0;
  border-radius: 8px;
  border: 3px solid #28a745;
}

.success-icon {
  font-size: 72px;
  color: #28a745;
  margin-bottom: 20px;
  font-weight: bold;
}

.feedback-success h2 {
  color: #28a745;
  margin-bottom: 15px;
  font-size: 28px;
  font-weight: 700;
}

.feedback-success p {
  color: #555;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 30px;
}

.submit-another-btn {
  background: linear-gradient(135deg, #28a745 0%, #20963c 100%);
  color: white;
  border: 2px solid #28a745;
  padding: 12px 30px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Times New Roman', serif;
}

.submit-another-btn:hover {
  background: linear-gradient(135deg, #20963c 0%, #155e28 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Contact Information */
.feedback-info {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 8px;
  border: 2px solid #ddd;
}

.feedback-info h3 {
  color: #d90000;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #ffd700;
  padding-bottom: 10px;
}

.contact-info {
  display: grid;
  gap: 15px;
}

.contact-item {
  font-size: 15px;
  line-height: 1.6;
}

.contact-item strong {
  color: #d90000;
  font-weight: 700;
  display: inline-block;
  min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feedback-container {
    padding: 20px 15px;
  }
  
  .feedback-header {
    padding: 20px;
  }
  
  .feedback-header h1 {
    font-size: 24px;
  }
  
  .feedback-form {
    padding: 25px 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feedback-info {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .feedback-header h1 {
    font-size: 20px;
  }
  
  .feedback-header p {
    font-size: 14px;
  }
  
  .submit-btn {
    padding: 12px 25px;
    font-size: 14px;
  }
}

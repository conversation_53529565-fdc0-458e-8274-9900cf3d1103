.article-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.article-detail {
  max-width: 900px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.article-header {
  padding: 30px 40px 20px;
  border-bottom: 1px solid #eee;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.article-category {
  background: #dc3545;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.article-date {
  color: #666;
  font-size: 14px;
}

.article-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  margin: 0 0 15px 0;
}

.article-summary {
  font-size: 16px;
  color: #555;
  line-height: 1.6;
  margin-bottom: 15px;
  font-style: italic;
  padding: 15px;
  background: #f8f9fa;
  border-left: 4px solid #dc3545;
}

.article-author {
  color: #666;
  font-size: 14px;
}

.article-image-container {
  width: 100%;
  max-height: 400px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
}

.article-image {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  display: block;
}

.article-content {
  padding: 40px;
  font-size: 16px;
  line-height: 1.8;
  color: #333;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  margin: 30px 0 15px 0;
  font-weight: bold;
  color: #333;
}

.article-content p {
  margin-bottom: 20px;
  text-align: justify;
}

.article-content img {
  max-width: 100%;
  height: auto;
  margin: 20px 0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.article-content ul,
.article-content ol {
  margin: 20px 0;
  padding-left: 30px;
}

.article-content li {
  margin-bottom: 8px;
}

.article-content blockquote {
  margin: 20px 0;
  padding: 15px 20px;
  background: #f8f9fa;
  border-left: 4px solid #dc3545;
  font-style: italic;
}

.article-footer {
  padding: 20px 40px 30px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

.article-footer .article-meta {
  margin-bottom: 0;
  font-size: 13px;
}

.loading,
.error {
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
}

.loading {
  color: #666;
}

.error {
  color: #dc3545;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
  .article-detail-container {
    padding: 10px;
  }
  
  .article-header {
    padding: 20px 25px 15px;
  }
  
  .article-content {
    padding: 25px;
  }
  
  .article-footer {
    padding: 15px 25px 20px;
  }
  
  .article-title {
    font-size: 24px;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

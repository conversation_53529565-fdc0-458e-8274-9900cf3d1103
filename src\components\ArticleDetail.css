.article-detail {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.article-header {
  margin-bottom: 30px;
}

.category-tag {
  display: inline-block;
  background: #1976d2;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.article-title {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
  margin: 15px 0;
}

.article-meta {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.article-summary {
  background: #f8f9fa;
  padding: 20px;
  border-left: 4px solid #1976d2;
  margin: 20px 0;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #444;
}

.article-image {
  margin: 30px 0;
  text-align: center;
}

.article-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.article-content {
  line-height: 1.8;
  font-size: 16px;
  color: #333;
  margin: 30px 0;
}

.article-content p {
  margin-bottom: 20px;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  margin: 30px 0 15px 0;
  color: #1976d2;
}

.article-content ul,
.article-content ol {
  margin: 15px 0;
  padding-left: 30px;
}

.article-content li {
  margin-bottom: 8px;
}

.article-content blockquote {
  border-left: 4px solid #1976d2;
  margin: 20px 0;
  padding: 15px 20px;
  background: #f8f9fa;
  font-style: italic;
}

.article-content img {
  max-width: 100%;
  height: auto;
  margin: 20px auto;
  display: block;
  border-radius: 4px;
}

.article-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.back-button {
  background: #1976d2;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: #1565c0;
}

.loading,
.error {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 18px;
}

.error {
  color: #d32f2f;
}

@media (max-width: 768px) {
  .article-detail {
    margin: 10px;
    padding: 15px;
  }
  
  .article-title {
    font-size: 1.5rem;
  }
  
  .article-meta {
    flex-direction: column;
    gap: 5px;
  }
  
  .article-content {
    font-size: 15px;
  }
}

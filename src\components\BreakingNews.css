.breaking-news {
  background-color: #fff3cd;
  padding: 10px 20px;
  font-size: 14px;
  border-bottom: 1px solid #e0c000;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
}

.label {
  font-weight: bold;
  margin-right: 10px;
  color: #b10000;
  position: relative;
  z-index: 2;
}

.scroll-text {
  display: inline-block;
  animation: scroll-left 20s linear infinite;
  padding-left: 10px;
  color: #333;
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-100%);
  }
}

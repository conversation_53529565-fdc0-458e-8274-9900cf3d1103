.category-section {
  margin-top: 40px;
  border-top: 3px solid #b10000;
  padding-top: 10px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #b10000;
  margin-bottom: 10px;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-item {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.news-item img {
  width: 150px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
}

.no-image-placeholder {
  width: 150px;
  height: 120px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
  flex-shrink: 0;
}

.no-image-placeholder::before {
  content: "Không có ảnh";
}

.news-item a {
  font-size: 14px;
  color: #333;
  text-decoration: none;
}

.news-item a:hover {
  text-decoration: underline;
  color: #b10000;
}
.category-grid {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.category-row {
  display: flex;
  gap: 32px;
}

.category-section {
  flex: 1;
}

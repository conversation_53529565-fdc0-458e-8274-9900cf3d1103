/* Dashboard Styles - Professional Government Theme */
.dashboard {
  padding: 0;
  background: transparent;
}

.dashboard-header {
  margin-bottom: 2rem;
  text-align: center;
  padding: 2rem 0;
  background: linear-gradient(135deg, rgba(200, 16, 46, 0.02) 0%, rgba(200, 16, 46, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(200, 16, 46, 0.1);
}

.dashboard-header h2 {
  margin: 0 0 0.5rem 0;
  color: #c8102e;
  font-size: 2rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dashboard-subtitle {
  color: #6c757d;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Statistics Overview */
.stats-overview {
  margin-bottom: 3rem;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  border-left: 4px solid #c8102e;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.1) 100%);
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.stat-card.primary {
  border-left-color: #c8102e;
}

.stat-card.warning {
  border-left-color: #ffc107;
}

.stat-card.danger {
  border-left-color: #dc3545;
}

.stat-card.success {
  border-left-color: #28a745;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.stat-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.stat-content h3 {
  margin: 0 0 0.8rem 0;
  color: #6c757d;
  font-size: 0.9rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1;
}

.stat-card.primary .stat-number {
  color: #c8102e;
}

.stat-card.warning .stat-number {
  color: #ffc107;
}

.stat-card.danger .stat-number {
  color: #dc3545;
}

.stat-card.success .stat-number {
  color: #28a745;
}

.stat-change {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 600;
}

/* Dashboard Content */
.dashboard-content {
  margin-bottom: 3rem;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  border-top: 4px solid #c8102e;
}

.chart-card h3 {
  margin: 0 0 2rem 0;
  color: #c8102e;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Category Bar Chart */
.category-bar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-weight: 600;
  color: #333;
}

.category-count {
  font-weight: 700;
  color: #c8102e;
  background: rgba(200, 16, 46, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.bar-container {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #c8102e 0%, #a50e26 100%);
  border-radius: 4px;
  transition: width 0.6s ease;
}

/* Status Chart */
.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #e9ecef;
  transition: all 0.3s ease;
}

.status-item:hover {
  background: rgba(200, 16, 46, 0.02);
  border-left-color: #c8102e;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: block;
}

.status-indicator.pending {
  background: #ffc107;
}

.status-indicator.inProgress {
  background: #17a2b8;
}

.status-indicator.resolved {
  background: #28a745;
}

.status-indicator.rejected {
  background: #dc3545;
}

.status-name {
  font-weight: 600;
  color: #333;
}

.status-count {
  font-weight: 700;
  color: #c8102e;
  background: rgba(200, 16, 46, 0.1);
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 1.1rem;
}

/* Activity Section */
.activity-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  border-top: 4px solid #c8102e;
}

.activity-section h3 {
  margin: 0 0 2rem 0;
  color: #c8102e;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #e9ecef;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(200, 16, 46, 0.02);
  border-left-color: #c8102e;
  transform: translateX(4px);
}

.activity-icon {
  font-size: 1.8rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(200, 16, 46, 0.1);
}

.activity-icon.article {
  background: rgba(23, 162, 184, 0.1);
}

.activity-icon.feedback {
  background: rgba(255, 193, 7, 0.1);
}

.activity-content {
  flex: 1;
}

.activity-description {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.3rem;
}

.activity-time {
  color: #6c757d;
  font-size: 0.9rem;
}

.activity-action {
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.activity-action.created {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.activity-action.updated {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.activity-action.resolved {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
}

/* Quick Actions */
.quick-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  border-top: 4px solid #c8102e;
}

.quick-actions h3 {
  margin: 0 0 2rem 0;
  color: #c8102e;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 1.5rem;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.action-btn:hover {
  background: rgba(200, 16, 46, 0.02);
  border-color: #c8102e;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(200, 16, 46, 0.1);
}

.action-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.action-text {
  font-weight: 600;
  color: #333;
  text-align: center;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-btn.create {
  border-color: #28a745;
}

.action-btn.create:hover {
  background: rgba(40, 167, 69, 0.02);
  border-color: #28a745;
}

.action-btn.review {
  border-color: #ffc107;
}

.action-btn.review:hover {
  background: rgba(255, 193, 7, 0.02);
  border-color: #ffc107;
}

.action-btn.report {
  border-color: #17a2b8;
}

.action-btn.report:hover {
  background: rgba(23, 162, 184, 0.02);
  border-color: #17a2b8;
}

.action-btn.settings {
  border-color: #6c757d;
}

.action-btn.settings:hover {
  background: rgba(108, 117, 125, 0.02);
  border-color: #6c757d;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .chart-card,
  .activity-section,
  .quick-actions {
    padding: 1.5rem;
  }
  
  .stat-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .activity-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .action-btn {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 1.5rem 1rem;
  }
  
  .dashboard-header h2 {
    font-size: 1.5rem;
  }
  
  .stats-row {
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}

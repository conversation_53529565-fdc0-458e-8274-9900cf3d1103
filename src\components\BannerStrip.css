.banner-strip {
  padding: 20px;
  margin-bottom: 40px; /* kho<PERSON>ng trắng dưới cùng */
}

.section-title {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: bold;
}

.banner-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scroll-button {
  background-color: #c8102e;
  color: white;
  border: none;
  padding: 10px;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.scroll-button:hover:not(:disabled) {
  background-color: #a10c22;
}

.scroll-button:disabled {
  opacity: 0.4;
  cursor: default;
}

.banner-container {
  overflow: hidden;
  width: 850px; /* 5 items x 170px (160 + 10 margin) */
  position: relative;
}

.banner-list {
  display: flex;
  transition: transform 0.7s cubic-bezier(0.23, 1, 0.32, 1); /* smooth và dẻo hơn */
  will-change: transform; /* hint cho trình duyệt tối <PERSON>u chuyển động */
}

.banner-item {
  width: 160px;
  height: 60px;
  flex-shrink: 0;
  margin-right: 10px;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.banner-item img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
